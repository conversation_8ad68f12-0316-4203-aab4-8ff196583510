oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.test.ts --testNamePattern="Memory Boundary Enforcement" --verbose --no-coverage --forceExit

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.test.ts --testNamePattern=Memory Boundary Enforcement --verbose --no-coverage --forceExit

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBuffer.test.ts (13.667 s, 382 MB heap size)


  ● Test suite failed to run

    thrown: "Exceeded timeout of 10000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      229 |
      230 |   // Global cleanup following Lesson 05 patterns
    > 231 |   afterAll(async () => {
          |   ^
      232 |     try {
      233 |       // Force global cleanup to ensure no resources leak
      234 |       const { MemorySafeResourceManager } = await import('../MemorySafeResourceManager');

      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:231:3
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:170:1)

Test Suites: 1 failed, 1 total
Tests:       106 skipped, 3 passed, 109 total
Snapshots:   0 total
Time:        13.921 s
Ran all test suites matching /AtomicCircularBuffer.test.ts/i with tests matching "Memory Boundary Enforcement".
dv@lnv:~/dev/web-dev/oa-prod$ npm test -- --testPathPattern=AtomicCircularBuffer.test.ts --verbose --no-coverage --forceExit

> oa-framework@1.0.0 test
> jest --testPathPattern=AtomicCircularBuffer.test.ts --verbose --no-coverage --forceExit

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/AtomicCircularBuffer.test.ts (32.448 s, 233 MB heap size)
  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Basic concurrent operations › should handle concurrent additions without corruption

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:351:20)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Basic concurrent operations › should handle mixed concurrent operations

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.removeItem (shared/src/base/AtomicCircularBuffer.ts:131:23)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:377:39)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Advanced race condition scenarios › should handle rapid key updates without data loss

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:409:32)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Advanced race condition scenarios › should handle concurrent add/remove on same keys

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.removeItem (shared/src/base/AtomicCircularBuffer.ts:131:23)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:434:39)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Advanced race condition scenarios › should handle concurrent operations with buffer overflow

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:460:34)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Advanced race condition scenarios › should handle concurrent clear operations

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.clear (shared/src/base/AtomicCircularBuffer.ts:182:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:489:18)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Stress testing and performance under concurrency › should handle high-concurrency stress test

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:522:28
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:529:13)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Stress testing and performance under concurrency › should maintain performance under sustained concurrent load

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at loadGenerator (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:560:26)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:569:25)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Stress testing and performance under concurrency › should handle concurrent operations with different data types

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:594:37)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Edge cases in concurrent scenarios › should handle concurrent operations on empty buffer

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.removeItem (shared/src/base/AtomicCircularBuffer.ts:131:23)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:628:32)

  ● AtomicCircularBuffer › Enhanced Concurrent Access and Race Conditions › Edge cases in concurrent scenarios › should handle concurrent operations during buffer state transitions

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:661:42)

  ● AtomicCircularBuffer › Comprehensive Metrics Testing › Metrics defensive copying › should handle metrics during concurrent access

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:821:32)

  ● AtomicCircularBuffer › Comprehensive Metrics Testing › Metrics edge cases and overflow scenarios › should handle metrics during rapid operations

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:891:32)

  ● AtomicCircularBuffer › Comprehensive Metrics Testing › Sync validation metrics › should track sync validations

    thrown: "Exceeded timeout of 10000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      924 |
      925 |     describe('Sync validation metrics', () => {
    > 926 |       it('should track sync validations', async () => {
          |       ^
      927 |         const initialMetrics = buffer.getMetrics();
      928 |
      929 |         // Add items to trigger sync validations

      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:926:7
      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:925:5
      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:720:3
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:170:1)

  ● AtomicCircularBuffer › Basic Functionality - Enhanced Coverage › Constructor and Initialization › should handle zero max size gracefully

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      1008 |         // Adding to zero-size buffer should not crash
      1009 |         await zeroBuffer.addItem('test', 'value');
    > 1010 |         expect(zeroBuffer.getSize()).toBe(0);
           |                                      ^
      1011 |         expect(zeroBuffer.getItem('test')).toBeUndefined();
      1012 |
      1013 |         await zeroBuffer.shutdown();

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:1010:38)

  ● AtomicCircularBuffer › Comprehensive Error Handling and Recovery › Buffer state error conditions › should handle buffer overflow edge cases

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:1389:40)

  ● AtomicCircularBuffer › Logging Interface (ILoggingService) › logError method › should log error messages with Error objects

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    Expected: "[ERROR] AtomicCircularBuffer: Test error message - Test error", ""
    Received: "[ERROR] AtomicCircularBuffer: Test error message - Error: Test error", ""

    Number of calls: 1

      1632 |         buffer.logError('Test error message', error);
      1633 |
    > 1634 |         expect(mockConsole.error).toHaveBeenCalledWith(
           |                                   ^
      1635 |           '[ERROR] AtomicCircularBuffer: Test error message - Test error',
      1636 |           ''
      1637 |         );

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:1634:35)

  ● AtomicCircularBuffer › Logging Interface (ILoggingService) › logError method › should log error messages with details

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    - Expected
    + Received

    - "[ERROR] AtomicCircularBuffer: Operation failed - Test error",
    + "[ERROR] AtomicCircularBuffer: Operation failed - Error: Test error",
      {"key": "testKey", "operation": "addItem"},

    Number of calls: 1

      1652 |         buffer.logError('Operation failed', error, details);
      1653 |
    > 1654 |         expect(mockConsole.error).toHaveBeenCalledWith(
           |                                   ^
      1655 |           '[ERROR] AtomicCircularBuffer: Operation failed - Test error',
      1656 |           details
      1657 |         );

      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:1654:35)

  ● AtomicCircularBuffer › Logging Interface (ILoggingService) › Logging integration with buffer operations › should handle logging during concurrent operations

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:1780:32)

  ● AtomicCircularBuffer › Synchronization and Validation › Concurrent access synchronization › should handle concurrent operations during buffer overflow

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:2245:40)

  ● AtomicCircularBuffer › Synchronization and Validation › Validation and error detection › should track sync validations in metrics

    thrown: "Exceeded timeout of 10000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      2307 |
      2308 |     describe('Validation and error detection', () => {
    > 2309 |       it('should track sync validations in metrics', async () => {
           |       ^
      2310 |         const initialMetrics = buffer.getMetrics();
      2311 |
      2312 |         // Add items to trigger sync validations

      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:2309:7
      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:2308:5
      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:2119:3
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:170:1)

  ● AtomicCircularBuffer › Synchronization and Validation › Validation and error detection › should handle validation during high-frequency operations

    Operation already in progress - test environment does not support concurrent operations

      199 |       // In test environment, execute synchronously to avoid timer-based deadlocks
      200 |       if (this._operationLock) {
    > 201 |         throw new Error('Operation already in progress - test environment does not support concurrent operations');
          |               ^
      202 |       }
      203 |
      204 |       this._operationLock = true;

      at AtomicCircularBuffer._withLock (shared/src/base/AtomicCircularBuffer.ts:201:15)
      at AtomicCircularBuffer.addItem (shared/src/base/AtomicCircularBuffer.ts:101:16)
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:2331:40)


  ● Test suite failed to run

    thrown: "Exceeded timeout of 10000 ms for a hook.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      229 |
      230 |   // Global cleanup following Lesson 05 patterns
    > 231 |   afterAll(async () => {
          |   ^
      232 |     try {
      233 |       // Force global cleanup to ensure no resources leak
      234 |       const { MemorySafeResourceManager } = await import('../MemorySafeResourceManager');

      at shared/src/base/__tests__/AtomicCircularBuffer.test.ts:231:3
      at Object.<anonymous> (shared/src/base/__tests__/AtomicCircularBuffer.test.ts:170:1)

Test Suites: 1 failed, 1 total
Tests:       22 failed, 87 passed, 109 total
Snapshots:   0 total
Time:        32.706 s
Ran all test suites matching /AtomicCircularBuffer.test.ts/i
